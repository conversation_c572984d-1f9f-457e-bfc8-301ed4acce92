from flask import Flask, jsonify, request
from flask_cors import CORS
import json
from market_data_service import MarketDataService
import threading
import time

app = Flask(__name__)
CORS(app)

# Initialize market data service
market_service = MarketDataService()

# Cache for market data
data_cache = {
    'portfolio': [],
    'market_overview': [],
    'paper_trading': {},
    'last_updated': 0
}

def update_market_data():
    """Background task to update market data every 5 minutes"""
    while True:
        try:
            print("Updating market data...")
            data_cache['portfolio'] = market_service.get_portfolio_data()
            data_cache['market_overview'] = market_service.get_market_overview()
            data_cache['paper_trading'] = market_service.generate_paper_trading_data()
            data_cache['last_updated'] = time.time()
            print("Market data updated successfully")
        except Exception as e:
            print(f"Error updating market data: {e}")
        
        time.sleep(300)  # Update every 5 minutes

# Start background data update thread
data_thread = threading.Thread(target=update_market_data, daemon=True)
data_thread.start()

@app.route('/api/portfolio', methods=['GET'])
def get_portfolio():
    """Get portfolio data"""
    return jsonify({
        'success': True,
        'data': data_cache['portfolio'],
        'last_updated': data_cache['last_updated']
    })

@app.route('/api/market-overview', methods=['GET'])
def get_market_overview():
    """Get market overview data"""
    return jsonify({
        'success': True,
        'data': data_cache['market_overview'],
        'last_updated': data_cache['last_updated']
    })

@app.route('/api/paper-trading', methods=['GET'])
def get_paper_trading():
    """Get paper trading portfolio data"""
    return jsonify({
        'success': True,
        'data': data_cache['paper_trading'],
        'last_updated': data_cache['last_updated']
    })

@app.route('/api/chart/<symbol>', methods=['GET'])
def get_chart_data(symbol):
    """Get chart data for a specific symbol"""
    range_period = request.args.get('range', '1mo')
    try:
        chart_data = market_service.get_chart_data(symbol.upper(), range_period)
        return jsonify({
            'success': True,
            'data': chart_data,
            'symbol': symbol.upper(),
            'range': range_period
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stock/<symbol>', methods=['GET'])
def get_stock_data(symbol):
    """Get detailed stock data for a specific symbol"""
    try:
        stock_data = market_service.get_stock_data(symbol.upper())
        return jsonify({
            'success': True,
            'data': stock_data,
            'symbol': symbol.upper()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/watchlist', methods=['GET'])
def get_watchlist():
    """Get watchlist data"""
    watchlist_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX', 'CRM', 'ADBE']
    watchlist_data = []
    
    for symbol in watchlist_symbols:
        try:
            data = market_service.get_stock_data(symbol, interval="1d", range_period="5d")
            if data and 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                result = data['chart']['result'][0]
                meta = result.get('meta', {})
                
                current_price = meta.get('regularMarketPrice', 0)
                previous_close = meta.get('chartPreviousClose', 0)
                change = current_price - previous_close if previous_close else 0
                change_percent = (change / previous_close * 100) if previous_close else 0
                
                watchlist_data.append({
                    'symbol': symbol,
                    'name': meta.get('shortName', symbol),
                    'price': current_price,
                    'change': change,
                    'changePercent': change_percent,
                    'volume': meta.get('regularMarketVolume', 0)
                })
        except Exception as e:
            print(f"Error fetching watchlist data for {symbol}: {e}")
    
    return jsonify({
        'success': True,
        'data': watchlist_data
    })

@app.route('/api/news', methods=['GET'])
def get_market_news():
    """Get market news (simulated for now)"""
    # This would typically fetch from a news API
    news_data = [
        {
            'id': 1,
            'title': 'Tech Stocks Rally on Strong Earnings Reports',
            'summary': 'Major technology companies report better-than-expected quarterly earnings, driving market optimism.',
            'source': 'Financial Times',
            'timestamp': int(time.time()) - 3600,
            'sentiment': 'positive'
        },
        {
            'id': 2,
            'title': 'Federal Reserve Maintains Interest Rates',
            'summary': 'The Fed keeps rates steady amid ongoing economic uncertainty and inflation concerns.',
            'source': 'Reuters',
            'timestamp': int(time.time()) - 7200,
            'sentiment': 'neutral'
        },
        {
            'id': 3,
            'title': 'Energy Sector Shows Mixed Performance',
            'summary': 'Oil prices fluctuate as global demand patterns shift and supply concerns persist.',
            'source': 'Bloomberg',
            'timestamp': int(time.time()) - 10800,
            'sentiment': 'mixed'
        }
    ]
    
    return jsonify({
        'success': True,
        'data': news_data
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': time.time(),
        'cache_last_updated': data_cache['last_updated']
    })

if __name__ == '__main__':
    # Initialize cache on startup
    try:
        print("Initializing market data cache...")
        data_cache['portfolio'] = market_service.get_portfolio_data()
        data_cache['market_overview'] = market_service.get_market_overview()
        data_cache['paper_trading'] = market_service.generate_paper_trading_data()
        data_cache['last_updated'] = time.time()
        print("Market data cache initialized successfully")
    except Exception as e:
        print(f"Error initializing cache: {e}")
    
    app.run(host='0.0.0.0', port=5000, debug=True)

