import sys
sys.path.append('/opt/.manus/.sandbox-runtime')
from data_api import ApiClient
import json
from datetime import datetime, timedelta
import random

class MarketDataService:
    def __init__(self):
        self.client = ApiClient()
    
    def get_stock_data(self, symbol="AAPL", region="US", interval="1d", range_period="1mo"):
        """Get stock chart data for a given symbol"""
        try:
            response = self.client.call_api('YahooFinance/get_stock_chart', query={
                'symbol': symbol,
                'region': region,
                'interval': interval,
                'range': range_period,
                'includePrePost': False,
                'includeAdjustedClose': True
            })
            return response
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None
    
    def get_portfolio_data(self):
        """Get portfolio data for multiple stocks"""
        portfolio_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META", "NFLX"]
        portfolio_data = []
        
        for symbol in portfolio_symbols:
            data = self.get_stock_data(symbol, interval="1d", range_period="5d")
            if data and 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                result = data['chart']['result'][0]
                meta = result.get('meta', {})
                
                # Calculate basic metrics
                current_price = meta.get('regularMarketPrice', 0)
                previous_close = meta.get('chartPreviousClose', 0)
                change = current_price - previous_close if previous_close else 0
                change_percent = (change / previous_close * 100) if previous_close else 0
                
                portfolio_data.append({
                    'symbol': symbol,
                    'name': meta.get('longName', symbol),
                    'price': current_price,
                    'change': change,
                    'changePercent': change_percent,
                    'volume': meta.get('regularMarketVolume', 0),
                    'dayHigh': meta.get('regularMarketDayHigh', 0),
                    'dayLow': meta.get('regularMarketDayLow', 0),
                    'fiftyTwoWeekHigh': meta.get('fiftyTwoWeekHigh', 0),
                    'fiftyTwoWeekLow': meta.get('fiftyTwoWeekLow', 0)
                })
        
        return portfolio_data
    
    def get_market_overview(self):
        """Get market overview data"""
        indices = ["^GSPC", "^DJI", "^IXIC", "^RUT"]  # S&P 500, Dow Jones, NASDAQ, Russell 2000
        market_data = []
        
        for symbol in indices:
            data = self.get_stock_data(symbol, interval="1d", range_period="5d")
            if data and 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                result = data['chart']['result'][0]
                meta = result.get('meta', {})
                
                current_price = meta.get('regularMarketPrice', 0)
                previous_close = meta.get('chartPreviousClose', 0)
                change = current_price - previous_close if previous_close else 0
                change_percent = (change / previous_close * 100) if previous_close else 0
                
                market_data.append({
                    'symbol': symbol,
                    'name': meta.get('shortName', symbol),
                    'price': current_price,
                    'change': change,
                    'changePercent': change_percent
                })
        
        return market_data
    
    def get_chart_data(self, symbol="AAPL", range_period="1mo"):
        """Get chart data for visualization"""
        data = self.get_stock_data(symbol, interval="1d", range_period=range_period)
        if not data or 'chart' not in data or 'result' not in data['chart'] or not data['chart']['result']:
            return None
        
        result = data['chart']['result'][0]
        timestamps = result.get('timestamp', [])
        indicators = result.get('indicators', {})
        quote = indicators.get('quote', [{}])[0] if indicators.get('quote') else {}
        
        chart_data = []
        for i, timestamp in enumerate(timestamps):
            if i < len(quote.get('close', [])):
                chart_data.append({
                    'date': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d'),
                    'timestamp': timestamp,
                    'open': quote.get('open', [])[i] if i < len(quote.get('open', [])) else None,
                    'high': quote.get('high', [])[i] if i < len(quote.get('high', [])) else None,
                    'low': quote.get('low', [])[i] if i < len(quote.get('low', [])) else None,
                    'close': quote.get('close', [])[i] if i < len(quote.get('close', [])) else None,
                    'volume': quote.get('volume', [])[i] if i < len(quote.get('volume', [])) else None
                })
        
        return chart_data
    
    def generate_paper_trading_data(self):
        """Generate realistic paper trading portfolio data"""
        # Simulate paper trading positions
        positions = [
            {'symbol': 'AAPL', 'shares': 50, 'avgPrice': 185.20, 'currentPrice': 0},
            {'symbol': 'GOOGL', 'shares': 25, 'avgPrice': 142.30, 'currentPrice': 0},
            {'symbol': 'MSFT', 'shares': 40, 'avgPrice': 378.50, 'currentPrice': 0},
            {'symbol': 'TSLA', 'shares': 15, 'avgPrice': 248.75, 'currentPrice': 0},
            {'symbol': 'NVDA', 'shares': 30, 'avgPrice': 875.20, 'currentPrice': 0}
        ]
        
        # Get current prices for positions
        total_value = 0
        total_cost = 0
        
        for position in positions:
            data = self.get_stock_data(position['symbol'], interval="1d", range_period="1d")
            if data and 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                meta = data['chart']['result'][0].get('meta', {})
                current_price = meta.get('regularMarketPrice', position['avgPrice'])
                position['currentPrice'] = current_price
                
                position_value = position['shares'] * current_price
                position_cost = position['shares'] * position['avgPrice']
                position['value'] = position_value
                position['cost'] = position_cost
                position['pnl'] = position_value - position_cost
                position['pnlPercent'] = (position['pnl'] / position_cost * 100) if position_cost else 0
                
                total_value += position_value
                total_cost += position_cost
        
        total_pnl = total_value - total_cost
        total_pnl_percent = (total_pnl / total_cost * 100) if total_cost else 0
        
        # Generate trading history
        trading_history = []
        for i in range(20):
            date = datetime.now() - timedelta(days=i*2)
            trading_history.append({
                'date': date.strftime('%Y-%m-%d'),
                'symbol': random.choice(['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META', 'AMZN']),
                'action': random.choice(['BUY', 'SELL']),
                'shares': random.randint(5, 50),
                'price': round(random.uniform(100, 500), 2),
                'pnl': round(random.uniform(-500, 1000), 2)
            })
        
        return {
            'positions': positions,
            'totalValue': total_value,
            'totalCost': total_cost,
            'totalPnL': total_pnl,
            'totalPnLPercent': total_pnl_percent,
            'tradingHistory': trading_history,
            'cashBalance': 25000.00,
            'totalPortfolioValue': total_value + 25000.00
        }

if __name__ == "__main__":
    service = MarketDataService()
    
    # Test the service
    print("Testing Market Data Service...")
    
    # Get portfolio data
    portfolio = service.get_portfolio_data()
    print(f"Portfolio data: {len(portfolio)} stocks")
    
    # Get market overview
    market = service.get_market_overview()
    print(f"Market overview: {len(market)} indices")
    
    # Get chart data
    chart = service.get_chart_data("AAPL", "1mo")
    print(f"Chart data: {len(chart) if chart else 0} data points")
    
    # Generate paper trading data
    paper_trading = service.generate_paper_trading_data()
    print(f"Paper trading: {len(paper_trading['positions'])} positions")
    
    print("Market Data Service is ready!")

