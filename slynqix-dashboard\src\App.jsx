import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { 
  BarChart3, 
  BookOpen, 
  Brain, 
  TrendingUp, 
  Globe, 
  Cpu, 
  LogIn,
  Github,
  Phone,
  Chrome,
  Menu,
  X,
  Home,
  User,
  Settings,
  DollarSign,
  Activity,
  Users,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Eye,
  Plus,
  Minus,
  Search,
  Filter,
  Calendar,
  Bell,
  Star
} from 'lucide-react'
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts'
import './App.css'

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [activeSection, setActiveSection] = useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [marketData, setMarketData] = useState({})
  const [portfolioData, setPortfolioData] = useState({})
  const [watchlist, setWatchlist] = useState([])
  const [selectedTimeframe, setSelectedTimeframe] = useState('1D')
  const [isLoading, setIsLoading] = useState(false)

  // Simulate real-time market data
  useEffect(() => {
    const updateMarketData = () => {
      const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA']
      const newMarketData = {}
      const newWatchlist = []
      
      symbols.forEach(symbol => {
        const basePrice = Math.random() * 400 + 100
        const change = (Math.random() - 0.5) * 20
        const changePercent = (change / basePrice) * 100
        
        newMarketData[symbol] = {
          price: basePrice.toFixed(2),
          change: change.toFixed(2),
          changePercent: changePercent.toFixed(2)
        }
        
        newWatchlist.push({
          symbol,
          name: `${symbol} Inc.`,
          price: basePrice.toFixed(2),
          change: change.toFixed(2),
          changePercent: changePercent.toFixed(2)
        })
      })
      
      setMarketData(newMarketData)
      setWatchlist(newWatchlist)
      
      // Update portfolio data
      setPortfolioData({
        totalValue: 84082 + Math.random() * 2000 - 1000,
        dayPnL: -324.15 + Math.random() * 200 - 100,
        dayPnLPercent: -1.59 + Math.random() * 2 - 1,
        positions: 5,
        cashBalance: 25000
      })
    }

    updateMarketData()
    const interval = setInterval(updateMarketData, 5000) // Update every 5 seconds
    
    return () => clearInterval(interval)
  }, [])

  const coreFeatures = [
    {
      id: 'console',
      title: 'Console',
      description: 'In-depth analysis tools for market symbols with statistical, visual, and indicator analysis',
      icon: BarChart3
    },
    {
      id: 'journal',
      title: 'Journal',
      description: 'Log and review your trading activities with detailed performance tracking',
      icon: BookOpen
    },
    {
      id: 'mindsage',
      title: 'Mindsage',
      description: 'AI-powered investment guide providing insights beyond OHLCV data',
      icon: Brain
    },
    {
      id: 'algo-trading',
      title: 'Algo Trading',
      description: 'Automate paper trades with custom algorithms and natural language commands',
      icon: TrendingUp
    },
    {
      id: 'global-sentiment',
      title: 'Global Sentiment',
      description: 'Analyze news sentiment from global financial platforms for market insights',
      icon: Globe
    },
    {
      id: 'model-trainer',
      title: 'Model Trainer',
      description: 'Train personalized AI models to act as your personal trading assistant',
      icon: Cpu
    }
  ]

  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'console', label: 'Console', icon: BarChart3 },
    { id: 'journal', label: 'Journal', icon: BookOpen },
    { id: 'mindsage', label: 'Mindsage', icon: Brain },
    { id: 'algo-trading', label: 'Algo Trading', icon: TrendingUp },
    { id: 'global-sentiment', label: 'Global Sentiment', icon: Globe },
    { id: 'model-trainer', label: 'Model Trainer', icon: Cpu },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings }
  ]

  const MetricCard = ({ title, value, change, icon: Icon, trend = 'neutral' }) => (
    <Card className="liquid-glass-card">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <p className={`text-sm ${
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {change}
              </p>
            )}
          </div>
          <div className={`p-3 rounded-full ${
            trend === 'up' ? 'bg-green-100' : 
            trend === 'down' ? 'bg-red-100' : 
            'bg-gray-100'
          }`}>
            <Icon className={`h-6 w-6 ${
              trend === 'up' ? 'text-green-600' : 
              trend === 'down' ? 'text-red-600' : 
              'text-gray-600'
            }`} />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const AuthButton = ({ provider, icon: Icon, onClick }) => (
    <Button 
      onClick={onClick}
      className="liquid-glass-button w-full flex items-center justify-center gap-3 py-3"
    >
      <Icon className="h-5 w-5" />
      Continue with {provider}
    </Button>
  )

  const generatePortfolioData = () => {
    const data = []
    for (let i = 0; i < 24; i++) {
      const hour = i.toString().padStart(2, '0') + ':00'
      const value = 84000 + Math.random() * 4000 - 2000
      data.push({
        time: hour,
        value: Math.round(value)
      })
    }
    return data
  }

  const renderDashboard = () => (
    <>
      <div className="space-y-6">
        {/* Market Overview Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Market Overview</h1>
            <p className="text-gray-600">Real-time portfolio and market data</p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <Bell className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <MetricCard
            title="Portfolio Value"
            value={`$${portfolioData.totalValue?.toFixed(2) || '84,082'}`}
            change={`+$1137.25 (1.96%)`}
            icon={DollarSign}
            trend="up"
          />
          <MetricCard
            title="Day's P&L"
            value={`$${portfolioData.dayPnL?.toFixed(2) || '-324.15'}`}
            change={`${portfolioData.dayPnLPercent?.toFixed(2) || '-1.59'}% (vs yesterday%)`}
            icon={TrendingUp}
            trend={portfolioData.dayPnL >= 0 ? 'up' : 'down'}
          />
          <MetricCard
            title="Active Positions"
            value={portfolioData.positions || '5'}
            icon={Activity}
            trend="neutral"
          />
          <MetricCard
            title="Cash Balance"
            value={`$${portfolioData.cashBalance?.toLocaleString() || '25,000'}`}
            icon={CreditCard}
            trend="neutral"
          />
        </div>

        {/* Portfolio Performance Chart */}
        <Card className="liquid-glass-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Portfolio Performance</CardTitle>
                <CardDescription>Real-time portfolio value tracking</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                {['1D', '1W', '1M', '3M', '1Y'].map((timeframe) => (
                  <Button
                    key={timeframe}
                    variant={selectedTimeframe === timeframe ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedTimeframe(timeframe)}
                    className="liquid-glass-button"
                  >
                    {timeframe}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={generatePortfolioData()}>
                <defs>
                  <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis dataKey="time" stroke="#6B7280" fontSize={12} />
                <YAxis stroke="#6B7280" fontSize={12} />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    backdropFilter: 'blur(10px)'
                  }} 
                />
                <Area type="monotone" dataKey="value" stroke="#3B82F6" strokeWidth={2} fillOpacity={1} fill="url(#colorValue)" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Portfolio Positions and Watchlist */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Portfolio Positions */}
          <Card className="liquid-glass-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Portfolio Positions</CardTitle>
                <Button size="sm" className="liquid-glass-button">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Position
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { symbol: 'AAPL', name: 'Apple Inc.', shares: 50, value: 9472.50, pnl: 212.50, pnlPercent: 2.29 },
                  { symbol: 'GOOGL', name: 'Alphabet Inc.', shares: 25, value: 3650.75, pnl: -89.25, pnlPercent: -2.39 },
                  { symbol: 'MSFT', name: 'Microsoft Corp.', shares: 30, value: 11559.00, pnl: 445.80, pnlPercent: 4.01 }
                ].map((position, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-bold text-sm">{position.symbol.slice(0, 2)}</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{position.symbol}</p>
                        <p className="text-sm text-gray-600">{position.shares} shares</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">${position.value.toFixed(2)}</p>
                      <p className={`text-sm ${position.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {position.pnl >= 0 ? '+' : ''}${position.pnl.toFixed(2)} ({position.pnlPercent.toFixed(2)}%)
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Watchlist */}
          <Card className="liquid-glass-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Watchlist</CardTitle>
                <Button size="sm" variant="outline" className="liquid-glass-button">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Symbol
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {watchlist.map((stock, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center">
                        <span className="text-white font-bold text-sm">{stock.symbol.slice(0, 2)}</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{stock.symbol}</p>
                        <p className="text-sm text-gray-600">{stock.name}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">${stock.price}</p>
                      <p className={`text-sm ${parseFloat(stock.change) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {parseFloat(stock.changePercent).toFixed(2)}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Core Features Grid */}
        <Card className="liquid-glass-card">
          <CardHeader>
            <CardTitle>Platform Features</CardTitle>
            <CardDescription>Access all Slynqix trading and analysis tools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {coreFeatures.map((feature) => {
                const IconComponent = feature.icon
                return (
                  <div
                    key={feature.id}
                    onClick={() => setActiveSection(feature.id)}
                    className="liquid-glass-card p-6 cursor-pointer hover:bg-white/20 transition-all duration-300 group"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 group-hover:from-purple-500 group-hover:to-blue-600 transition-all duration-300">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )

  const renderConsole = () => {
    const [selectedSymbol, setSelectedSymbol] = useState('AAPL')
    const [consoleTab, setConsoleTab] = useState('analysis')
    const [pipMode, setPipMode] = useState(false)
    
    // Generate technical indicators data
    const generateTechnicalData = () => {
      const data = []
      let price = 150
      for (let i = 0; i < 50; i++) {
        price += (Math.random() - 0.5) * 5
        const rsi = 30 + Math.random() * 40
        const macd = (Math.random() - 0.5) * 2
        data.push({
          date: new Date(Date.now() - (49 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
          price: Math.round(price * 100) / 100,
          volume: Math.floor(Math.random() * 10000000) + 1000000,
          rsi: Math.round(rsi * 100) / 100,
          macd: Math.round(macd * 100) / 100,
          sma20: Math.round((price + (Math.random() - 0.5) * 10) * 100) / 100,
          sma50: Math.round((price + (Math.random() - 0.5) * 15) * 100) / 100
        })
      }
      return data
    }

    const technicalData = generateTechnicalData()
    const currentPrice = technicalData[technicalData.length - 1]?.price || 150
    const priceChange = (Math.random() - 0.5) * 10
    const priceChangePercent = (priceChange / currentPrice) * 100

    const consoleTabs = [
      { id: 'analysis', label: 'Analysis', icon: BarChart3 },
      { id: 'indicators', label: 'Indicators', icon: TrendingUp },
      { id: 'suggestions', label: 'Suggestions', icon: Brain },
      { id: 'aftermarket', label: 'Aftermarket', icon: Calendar },
      { id: 'history', label: 'History', icon: BookOpen }
    ]

    const suggestions = [
      {
        type: 'BUY',
        confidence: 85,
        reason: 'Strong upward momentum with RSI oversold conditions',
        target: currentPrice * 1.15,
        stopLoss: currentPrice * 0.95
      },
      {
        type: 'HOLD',
        confidence: 72,
        reason: 'Consolidation pattern forming near resistance level',
        target: currentPrice * 1.08,
        stopLoss: currentPrice * 0.92
      },
      {
        type: 'WATCH',
        confidence: 68,
        reason: 'Volume declining, waiting for breakout confirmation',
        target: currentPrice * 1.12,
        stopLoss: currentPrice * 0.88
      }
    ]

    return (
      <div className="space-y-6">
        {/* Console Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Console</h1>
            <p className="text-gray-600">Advanced stock analysis and trading tools</p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant={pipMode ? "default" : "outline"}
              onClick={() => setPipMode(!pipMode)}
              className="liquid-glass-button"
            >
              <Eye className="h-4 w-4 mr-2" />
              PiP Mode
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Symbol Search */}
        <Card className="liquid-glass-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Enter symbol (e.g., AAPL, GOOGL, MSFT)"
                    value={selectedSymbol}
                    onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
                  />
                </div>
              </div>
              <Button className="liquid-glass-button">
                Analyze
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Current Symbol Info */}
        <Card className="liquid-glass-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-bold">{selectedSymbol.slice(0, 2)}</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedSymbol}</h2>
                  <p className="text-gray-600">{selectedSymbol} Inc.</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900">${currentPrice.toFixed(2)}</p>
                <p className={`text-lg ${priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {priceChange >= 0 ? '+' : ''}${priceChange.toFixed(2)} ({priceChangePercent.toFixed(2)}%)
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Console Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {consoleTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setConsoleTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  consoleTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Console Content */}
        {consoleTab === 'analysis' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Price Chart */}
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Price Chart & Volume</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={technicalData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                    <XAxis dataKey="date" stroke="#6B7280" fontSize={12} />
                    <YAxis stroke="#6B7280" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                        border: '1px solid #E5E7EB',
                        borderRadius: '8px',
                        backdropFilter: 'blur(10px)'
                      }} 
                    />
                    <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} dot={false} />
                    <Line type="monotone" dataKey="sma20" stroke="#10B981" strokeWidth={1} strokeDasharray="5 5" dot={false} />
                    <Line type="monotone" dataKey="sma50" stroke="#F59E0B" strokeWidth={1} strokeDasharray="5 5" dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Statistical Analysis */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Statistical Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">52W High</span>
                    <span className="font-semibold">${(currentPrice * 1.25).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">52W Low</span>
                    <span className="font-semibold">${(currentPrice * 0.75).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Volatility (30D)</span>
                    <span className="font-semibold">{(Math.random() * 30 + 15).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Beta</span>
                    <span className="font-semibold">{(Math.random() * 2 + 0.5).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Market Cap</span>
                    <span className="font-semibold">${(Math.random() * 2000 + 500).toFixed(0)}B</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">P/E Ratio</span>
                    <span className="font-semibold">{(Math.random() * 30 + 10).toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Volume Analysis */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Volume Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={technicalData.slice(-10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" fontSize={10} />
                    <YAxis fontSize={10} />
                    <Tooltip />
                    <Bar dataKey="volume" fill="#3B82F6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        )}

        {consoleTab === 'indicators' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* RSI Chart */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>RSI (Relative Strength Index)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={technicalData.slice(-20)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" fontSize={10} />
                    <YAxis domain={[0, 100]} fontSize={10} />
                    <Tooltip />
                    <Line type="monotone" dataKey="rsi" stroke="#8B5CF6" strokeWidth={2} />
                    <Line type="monotone" dataKey={() => 70} stroke="#EF4444" strokeDasharray="5 5" strokeWidth={1} />
                    <Line type="monotone" dataKey={() => 30} stroke="#10B981" strokeDasharray="5 5" strokeWidth={1} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* MACD Chart */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>MACD</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={technicalData.slice(-20)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" fontSize={10} />
                    <YAxis fontSize={10} />
                    <Tooltip />
                    <Line type="monotone" dataKey="macd" stroke="#F59E0B" strokeWidth={2} />
                    <Line type="monotone" dataKey={() => 0} stroke="#6B7280" strokeDasharray="5 5" strokeWidth={1} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Technical Summary */}
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Technical Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">RSI (14)</p>
                    <p className="text-2xl font-bold text-gray-900">{technicalData[technicalData.length - 1]?.rsi.toFixed(2)}</p>
                    <p className="text-sm text-gray-600">
                      {technicalData[technicalData.length - 1]?.rsi > 70 ? 'Overbought' : 
                       technicalData[technicalData.length - 1]?.rsi < 30 ? 'Oversold' : 'Neutral'}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">MACD</p>
                    <p className="text-2xl font-bold text-gray-900">{technicalData[technicalData.length - 1]?.macd.toFixed(2)}</p>
                    <p className="text-sm text-gray-600">
                      {technicalData[technicalData.length - 1]?.macd > 0 ? 'Bullish' : 'Bearish'}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Price vs SMA20</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {((currentPrice / technicalData[technicalData.length - 1]?.sma20 - 1) * 100).toFixed(2)}%
                    </p>
                    <p className="text-sm text-gray-600">
                      {currentPrice > technicalData[technicalData.length - 1]?.sma20 ? 'Above' : 'Below'} SMA20
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {consoleTab === 'suggestions' && (
          <div className="space-y-6">
            {suggestions.map((suggestion, index) => (
              <Card key={index} className="liquid-glass-card">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        suggestion.type === 'BUY' ? 'bg-green-100 text-green-800' :
                        suggestion.type === 'SELL' ? 'bg-red-100 text-red-800' :
                        suggestion.type === 'HOLD' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {suggestion.type}
                      </span>
                      <div>
                        <p className="font-semibold text-gray-900">Confidence: {suggestion.confidence}%</p>
                        <p className="text-sm text-gray-600">AI Recommendation</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Target: ${suggestion.target.toFixed(2)}</p>
                      <p className="text-sm text-gray-600">Stop Loss: ${suggestion.stopLoss.toFixed(2)}</p>
                    </div>
                  </div>
                  <p className="text-gray-700">{suggestion.reason}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {consoleTab === 'aftermarket' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Aftermarket Analysis</CardTitle>
              <CardDescription>Historical performance and patterns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-sm text-gray-600">Avg Daily Return</p>
                  <p className="text-2xl font-bold text-green-600">+0.85%</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-600">Win Rate (30D)</p>
                  <p className="text-2xl font-bold text-gray-900">64%</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-600">Max Drawdown</p>
                  <p className="text-2xl font-bold text-red-600">-12.5%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {consoleTab === 'history' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Analysis History</CardTitle>
              <CardDescription>Previous analysis sessions and recommendations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { date: '2024-06-10', symbol: 'AAPL', recommendation: 'BUY', confidence: 85, outcome: 'Pending' },
                  { date: '2024-06-09', symbol: 'GOOGL', recommendation: 'HOLD', confidence: 72, outcome: '+2.3%' },
                  { date: '2024-06-08', symbol: 'MSFT', recommendation: 'BUY', confidence: 90, outcome: '+5.1%' }
                ].map((session, index) => (
                  <div key={index} className="p-4 rounded-lg bg-white/10 border border-white/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-600">{session.date}</span>
                        <span className="font-semibold text-gray-900">{session.symbol}</span>
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          session.recommendation === 'BUY' ? 'bg-green-100 text-green-800' :
                          session.recommendation === 'SELL' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {session.recommendation}
                        </span>
                        <span className="text-sm text-gray-600">{session.confidence}% confidence</span>
                      </div>
                      <span className={`font-semibold ${
                        session.outcome === 'Pending' ? 'text-gray-600' :
                        session.outcome.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {session.outcome}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  const renderJournal = () => {
    const [journalTab, setJournalTab] = useState('trades')
    const [selectedTrade, setSelectedTrade] = useState(null)
    
    // Generate trading history data
    const generateTradingHistory = () => {
      const trades = []
      const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX']
      
      for (let i = 0; i < 25; i++) {
        const symbol = symbols[Math.floor(Math.random() * symbols.length)]
        const action = Math.random() > 0.5 ? 'BUY' : 'SELL'
        const shares = Math.floor(Math.random() * 100) + 10
        const price = Math.round((Math.random() * 400 + 100) * 100) / 100
        const pnl = Math.round((Math.random() - 0.5) * 2000 * 100) / 100
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        
        trades.push({
          id: i + 1,
          date: date.toLocaleDateString(),
          time: date.toLocaleTimeString(),
          symbol,
          action,
          shares,
          price,
          total: shares * price,
          pnl,
          pnlPercent: (pnl / (shares * price)) * 100,
          strategy: ['Momentum', 'Mean Reversion', 'Breakout', 'Swing'][Math.floor(Math.random() * 4)],
          notes: `${action} signal based on technical analysis`,
          status: Math.random() > 0.2 ? 'Completed' : 'Pending'
        })
      }
      
      return trades.sort((a, b) => new Date(b.date) - new Date(a.date))
    }

    const trades = generateTradingHistory()
    const totalPnL = trades.reduce((sum, trade) => sum + trade.pnl, 0)
    const winningTrades = trades.filter(trade => trade.pnl > 0).length
    const winRate = (winningTrades / trades.length) * 100

    const journalTabs = [
      { id: 'trades', label: 'Trade History', icon: BarChart3 },
      { id: 'performance', label: 'Performance', icon: TrendingUp },
      { id: 'analytics', label: 'Analytics', icon: Activity },
      { id: 'notes', label: 'Notes', icon: BookOpen }
    ]

    return (
      <div className="space-y-6">
        {/* Journal Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Trading Journal</h1>
            <p className="text-gray-600">Track and analyze your trading performance</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="liquid-glass-button">
              <Plus className="h-4 w-4 mr-2" />
              Add Trade
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <MetricCard
            title="Total P&L"
            value={`$${totalPnL.toFixed(2)}`}
            change={`${trades.length} trades`}
            icon={DollarSign}
            trend={totalPnL >= 0 ? 'up' : 'down'}
          />
          <MetricCard
            title="Win Rate"
            value={`${winRate.toFixed(1)}%`}
            change={`${winningTrades}/${trades.length}`}
            icon={TrendingUp}
            trend={winRate > 60 ? 'up' : winRate > 40 ? 'neutral' : 'down'}
          />
          <MetricCard
            title="Avg Trade"
            value={`$${(totalPnL / trades.length).toFixed(2)}`}
            icon={Activity}
            trend="neutral"
          />
          <MetricCard
            title="Best Trade"
            value={`$${Math.max(...trades.map(t => t.pnl)).toFixed(2)}`}
            icon={Star}
            trend="up"
          />
        </div>

        {/* Journal Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {journalTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setJournalTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  journalTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Journal Content */}
        {journalTab === 'trades' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Recent Trades</CardTitle>
              <CardDescription>Complete trading history with performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {trades.slice(0, 10).map((trade) => (
                  <div 
                    key={trade.id} 
                    className="p-4 rounded-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-colors cursor-pointer"
                    onClick={() => setSelectedTrade(trade)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-bold text-sm">{trade.symbol.slice(0, 2)}</span>
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded text-xs font-semibold ${
                              trade.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {trade.action}
                            </span>
                            <span className="font-semibold text-gray-900">{trade.symbol}</span>
                            <span className="text-gray-600">{trade.shares} shares @ ${trade.price}</span>
                          </div>
                          <p className="text-sm text-gray-600">{trade.date} • {trade.strategy}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {trade.pnl >= 0 ? '+' : ''}${trade.pnl.toFixed(2)}
                        </p>
                        <p className={`text-sm ${trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {trade.pnlPercent >= 0 ? '+' : ''}{trade.pnlPercent.toFixed(2)}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {journalTab === 'performance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* P&L Chart */}
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Cumulative P&L</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trades.slice(0, 15).reverse().map((trade, index) => ({
                    date: trade.date,
                    cumulative: trades.slice(0, index + 1).reduce((sum, t) => sum + t.pnl, 0)
                  }))}>
                    <defs>
                      <linearGradient id="colorPnL" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                    <XAxis dataKey="date" stroke="#6B7280" fontSize={12} />
                    <YAxis stroke="#6B7280" fontSize={12} />
                    <Tooltip />
                    <Area type="monotone" dataKey="cumulative" stroke="#10B981" strokeWidth={2} fillOpacity={1} fill="url(#colorPnL)" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Win/Loss Distribution */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Win/Loss Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Wins', value: winningTrades, fill: '#10B981' },
                        { name: 'Losses', value: trades.length - winningTrades, fill: '#EF4444' }
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      dataKey="value"
                    />
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Strategy Performance */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Strategy Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Momentum', 'Mean Reversion', 'Breakout', 'Swing'].map((strategy) => {
                    const strategyTrades = trades.filter(t => t.strategy === strategy)
                    const strategyPnL = strategyTrades.reduce((sum, t) => sum + t.pnl, 0)
                    const strategyWinRate = (strategyTrades.filter(t => t.pnl > 0).length / strategyTrades.length) * 100
                    
                    return (
                      <div key={strategy} className="flex items-center justify-between p-3 rounded-lg bg-white/10">
                        <div>
                          <p className="font-semibold text-gray-900">{strategy}</p>
                          <p className="text-sm text-gray-600">{strategyTrades.length} trades</p>
                        </div>
                        <div className="text-right">
                          <p className={`font-bold ${strategyPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            ${strategyPnL.toFixed(2)}
                          </p>
                          <p className="text-sm text-gray-600">{strategyWinRate.toFixed(1)}% win rate</p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {journalTab === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Trading Patterns */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Trading Patterns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average Hold Time</span>
                    <span className="font-semibold">{Math.floor(Math.random() * 5 + 1)} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Most Traded Symbol</span>
                    <span className="font-semibold">AAPL</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Best Trading Day</span>
                    <span className="font-semibold">Monday</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Risk per Trade</span>
                    <span className="font-semibold">{(Math.random() * 3 + 1).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sharpe Ratio</span>
                    <span className="font-semibold">{(Math.random() * 2 + 0.5).toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Monthly Performance */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={[
                    { month: 'Jan', pnl: Math.random() * 2000 - 1000 },
                    { month: 'Feb', pnl: Math.random() * 2000 - 1000 },
                    { month: 'Mar', pnl: Math.random() * 2000 - 1000 },
                    { month: 'Apr', pnl: Math.random() * 2000 - 1000 },
                    { month: 'May', pnl: Math.random() * 2000 - 1000 },
                    { month: 'Jun', pnl: Math.random() * 2000 - 1000 }
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="pnl" fill="#3B82F6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        )}

        {journalTab === 'notes' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Trading Notes & Insights</CardTitle>
              <CardDescription>Document your trading thoughts and lessons learned</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">Market Analysis - {new Date().toLocaleDateString()}</h3>
                    <Button variant="outline" size="sm" className="liquid-glass-button">Edit</Button>
                  </div>
                  <p className="text-gray-700">Strong momentum in tech sector. AAPL showing bullish patterns with RSI indicating potential continuation. Consider increasing position size on next pullback.</p>
                </div>
                
                <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">Lesson Learned - Risk Management</h3>
                    <Button variant="outline" size="sm" className="liquid-glass-button">Edit</Button>
                  </div>
                  <p className="text-gray-700">Need to stick to 2% risk per trade rule. Last week's TSLA trade exceeded risk tolerance and resulted in larger than expected loss.</p>
                </div>
                
                <Button className="liquid-glass-button w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Note
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Trade Detail Modal */}
        {selectedTrade && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setSelectedTrade(null)}>
            <Card className="liquid-glass-card w-full max-w-2xl m-4" onClick={(e) => e.stopPropagation()}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Trade Details - {selectedTrade.symbol}</CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedTrade(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Date & Time</p>
                    <p className="font-semibold">{selectedTrade.date} {selectedTrade.time}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Action</p>
                    <p className="font-semibold">{selectedTrade.action}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Shares</p>
                    <p className="font-semibold">{selectedTrade.shares}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Price</p>
                    <p className="font-semibold">${selectedTrade.price}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Value</p>
                    <p className="font-semibold">${selectedTrade.total.toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">P&L</p>
                    <p className={`font-semibold ${selectedTrade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${selectedTrade.pnl.toFixed(2)} ({selectedTrade.pnlPercent.toFixed(2)}%)
                    </p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm text-gray-600">Strategy</p>
                    <p className="font-semibold">{selectedTrade.strategy}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm text-gray-600">Notes</p>
                    <p className="font-semibold">{selectedTrade.notes}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    )
  }

  const renderMindsage = () => {
    const [mindsageTab, setMindsageTab] = useState('insights')
    const [selectedInsight, setSelectedInsight] = useState(null)
    
    const mindsageTabs = [
      { id: 'insights', label: 'AI Insights', icon: Brain },
      { id: 'market', label: 'Market Analysis', icon: Globe },
      { id: 'predictions', label: 'Predictions', icon: TrendingUp },
      { id: 'research', label: 'Research', icon: BookOpen }
    ]

    const aiInsights = [
      {
        id: 1,
        title: 'Tech Sector Rotation Detected',
        confidence: 92,
        type: 'Market Trend',
        summary: 'AI analysis indicates a significant rotation from growth tech to value tech stocks based on earnings momentum and institutional flow patterns.',
        details: 'Our machine learning models have identified unusual institutional buying patterns in value-oriented technology stocks while detecting systematic selling in high-growth names. This rotation appears to be driven by changing interest rate expectations and earnings quality concerns.',
        recommendations: ['Consider AAPL, MSFT over NVDA, TSLA', 'Monitor semiconductor ETFs', 'Watch for reversal signals'],
        timeframe: '2-4 weeks',
        riskLevel: 'Medium'
      },
      {
        id: 2,
        title: 'Earnings Season Alpha Opportunity',
        confidence: 87,
        type: 'Trading Signal',
        summary: 'Historical pattern analysis suggests specific sectors will outperform during upcoming earnings season based on guidance trends.',
        details: 'Analysis of 10 years of earnings data reveals that companies in the cloud infrastructure and cybersecurity sectors tend to beat expectations by wider margins during Q4 earnings season.',
        recommendations: ['Focus on CRM, SNOW, CRWD', 'Avoid retail and consumer discretionary', 'Consider earnings straddles'],
        timeframe: '3-6 weeks',
        riskLevel: 'High'
      },
      {
        id: 3,
        title: 'Volatility Regime Change',
        confidence: 78,
        type: 'Risk Alert',
        summary: 'Market microstructure analysis indicates we may be entering a higher volatility regime based on options flow and correlation patterns.',
        details: 'Cross-asset correlation analysis and options market indicators suggest increased market stress. VIX term structure is showing signs of backwardation, and institutional hedging activity has increased significantly.',
        recommendations: ['Reduce position sizes', 'Increase cash allocation', 'Consider protective puts'],
        timeframe: '1-2 months',
        riskLevel: 'High'
      }
    ]

    const marketAnalysis = {
      sentiment: 'Cautiously Optimistic',
      sentimentScore: 65,
      keyFactors: [
        { factor: 'Economic Data', impact: 'Positive', weight: 25 },
        { factor: 'Earnings Growth', impact: 'Neutral', weight: 30 },
        { factor: 'Geopolitical Risk', impact: 'Negative', weight: 20 },
        { factor: 'Monetary Policy', impact: 'Positive', weight: 25 }
      ],
      sectorOutlook: [
        { sector: 'Technology', outlook: 'Positive', score: 75 },
        { sector: 'Healthcare', outlook: 'Positive', score: 70 },
        { sector: 'Financials', outlook: 'Neutral', score: 55 },
        { sector: 'Energy', outlook: 'Negative', score: 40 },
        { sector: 'Consumer Discretionary', outlook: 'Neutral', score: 50 }
      ]
    }

    return (
      <div className="space-y-6">
        {/* Mindsage Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mindsage</h1>
            <p className="text-gray-600">AI-powered investment insights and market intelligence</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="liquid-glass-button">
              <Brain className="h-4 w-4 mr-2" />
              Generate Insights
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* AI Status */}
        <Card className="liquid-glass-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">AI Analysis Engine</h2>
                  <p className="text-gray-600">Processing 1,247 data sources • Last updated 2 minutes ago</p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                  <span className="text-green-600 font-semibold">Active</span>
                </div>
                <p className="text-sm text-gray-600">Confidence: 89%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mindsage Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {mindsageTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setMindsageTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  mindsageTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Mindsage Content */}
        {mindsageTab === 'insights' && (
          <div className="space-y-6">
            {aiInsights.map((insight) => (
              <Card key={insight.id} className="liquid-glass-card cursor-pointer hover:bg-white/20 transition-colors" onClick={() => setSelectedInsight(insight)}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        insight.confidence >= 90 ? 'bg-green-500' :
                        insight.confidence >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900">{insight.title}</h3>
                        <p className="text-sm text-gray-600">{insight.type} • {insight.confidence}% confidence</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        insight.riskLevel === 'High' ? 'bg-red-100 text-red-800' :
                        insight.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {insight.riskLevel} Risk
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">{insight.summary}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-gray-600">Timeframe: {insight.timeframe}</span>
                      <span className="text-sm text-gray-600">{insight.recommendations.length} recommendations</span>
                    </div>
                    <Button variant="outline" size="sm" className="liquid-glass-button">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {mindsageTab === 'market' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Market Sentiment */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Market Sentiment Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                    <span className="text-2xl font-bold text-white">{marketAnalysis.sentimentScore}</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{marketAnalysis.sentiment}</h3>
                  <p className="text-gray-600">Overall market sentiment score</p>
                </div>
                
                <div className="space-y-3">
                  {marketAnalysis.keyFactors.map((factor, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-700">{factor.factor}</span>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          factor.impact === 'Positive' ? 'bg-green-100 text-green-800' :
                          factor.impact === 'Negative' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {factor.impact}
                        </span>
                        <span className="text-sm text-gray-600">{factor.weight}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Sector Outlook */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Sector Outlook</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {marketAnalysis.sectorOutlook.map((sector, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">{sector.sector}</span>
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          sector.outlook === 'Positive' ? 'bg-green-100 text-green-800' :
                          sector.outlook === 'Negative' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {sector.outlook}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            sector.score >= 70 ? 'bg-green-500' :
                            sector.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${sector.score}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {mindsageTab === 'predictions' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Price Predictions */}
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>AI Price Predictions</CardTitle>
                <CardDescription>Machine learning models predict price movements for next 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {['AAPL', 'GOOGL', 'MSFT'].map((symbol) => {
                    const currentPrice = Math.round((Math.random() * 400 + 100) * 100) / 100
                    const predictedPrice = currentPrice * (1 + (Math.random() - 0.5) * 0.2)
                    const confidence = Math.floor(Math.random() * 30 + 60)
                    
                    return (
                      <div key={symbol} className="p-4 rounded-lg bg-white/10 border border-white/20">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-bold text-gray-900">{symbol}</h3>
                          <span className="text-sm text-gray-600">{confidence}% confidence</span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Current</span>
                            <span className="font-semibold">${currentPrice.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">30-day target</span>
                            <span className={`font-semibold ${predictedPrice > currentPrice ? 'text-green-600' : 'text-red-600'}`}>
                              ${predictedPrice.toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Expected return</span>
                            <span className={`font-semibold ${predictedPrice > currentPrice ? 'text-green-600' : 'text-red-600'}`}>
                              {((predictedPrice - currentPrice) / currentPrice * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {mindsageTab === 'research' && (
          <div className="space-y-6">
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>AI Research Reports</CardTitle>
                <CardDescription>Comprehensive analysis generated by our AI research team</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { title: 'Q4 2024 Technology Sector Deep Dive', date: '2 days ago', type: 'Sector Analysis' },
                    { title: 'Federal Reserve Policy Impact on Growth Stocks', date: '1 week ago', type: 'Macro Analysis' },
                    { title: 'Emerging Market Opportunities in 2025', date: '2 weeks ago', type: 'Geographic Analysis' },
                    { title: 'ESG Investing: Performance vs Principles', date: '3 weeks ago', type: 'Thematic Analysis' }
                  ].map((report, index) => (
                    <div key={index} className="p-4 rounded-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">{report.title}</h3>
                          <p className="text-sm text-gray-600">{report.type} • {report.date}</p>
                        </div>
                        <Button variant="outline" size="sm" className="liquid-glass-button">
                          Read Report
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Insight Detail Modal */}
        {selectedInsight && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setSelectedInsight(null)}>
            <Card className="liquid-glass-card w-full max-w-4xl m-4 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{selectedInsight.title}</CardTitle>
                    <CardDescription>{selectedInsight.type} • {selectedInsight.confidence}% confidence</CardDescription>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedInsight(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Analysis</h3>
                    <p className="text-gray-700">{selectedInsight.details}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Recommendations</h3>
                    <ul className="space-y-2">
                      {selectedInsight.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          <span className="text-gray-700">{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900">Timeframe</h4>
                      <p className="text-gray-700">{selectedInsight.timeframe}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Risk Level</h4>
                      <p className="text-gray-700">{selectedInsight.riskLevel}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    )
  }

  const renderAlgoTrading = () => {
    const [algoTab, setAlgoTab] = useState('strategies')
    const [selectedStrategy, setSelectedStrategy] = useState(null)
    
    const algoTabs = [
      { id: 'strategies', label: 'Strategies', icon: TrendingUp },
      { id: 'builder', label: 'Strategy Builder', icon: Cpu },
      { id: 'backtest', label: 'Backtesting', icon: BarChart3 },
      { id: 'live', label: 'Live Trading', icon: Activity }
    ]

    const strategies = [
      {
        id: 1,
        name: 'Momentum Breakout',
        description: 'Identifies stocks breaking above resistance with high volume',
        status: 'Active',
        performance: 15.7,
        trades: 23,
        winRate: 68,
        maxDrawdown: -8.2,
        sharpe: 1.45,
        lastTrade: '2 hours ago',
        riskLevel: 'Medium'
      },
      {
        id: 2,
        name: 'Mean Reversion',
        description: 'Buys oversold conditions and sells overbought levels',
        status: 'Paused',
        performance: 8.3,
        trades: 45,
        winRate: 72,
        maxDrawdown: -5.1,
        sharpe: 1.12,
        lastTrade: '1 day ago',
        riskLevel: 'Low'
      },
      {
        id: 3,
        name: 'Earnings Momentum',
        description: 'Trades around earnings announcements based on sentiment',
        status: 'Active',
        performance: 22.1,
        trades: 12,
        winRate: 58,
        maxDrawdown: -12.5,
        sharpe: 1.78,
        lastTrade: '30 minutes ago',
        riskLevel: 'High'
      }
    ]

    return (
      <div className="space-y-6">
        {/* Algo Trading Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Algo Trading</h1>
            <p className="text-gray-600">Automated paper trading with custom algorithms</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="liquid-glass-button">
              <Plus className="h-4 w-4 mr-2" />
              New Strategy
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Trading Status */}
        <Card className="liquid-glass-card">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-green-100 flex items-center justify-center">
                  <Activity className="h-8 w-8 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">2</p>
                <p className="text-gray-600">Active Strategies</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-blue-100 flex items-center justify-center">
                  <DollarSign className="h-8 w-8 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">$12,450</p>
                <p className="text-gray-600">Total P&L</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-purple-100 flex items-center justify-center">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">67%</p>
                <p className="text-gray-600">Win Rate</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-yellow-100 flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">80</p>
                <p className="text-gray-600">Total Trades</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Algo Trading Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {algoTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setAlgoTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  algoTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Algo Trading Content */}
        {algoTab === 'strategies' && (
          <div className="space-y-6">
            {strategies.map((strategy) => (
              <Card key={strategy.id} className="liquid-glass-card">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-4 h-4 rounded-full ${
                        strategy.status === 'Active' ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{strategy.name}</h3>
                        <p className="text-gray-600">{strategy.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        strategy.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {strategy.status}
                      </span>
                      <Button variant="outline" size="sm" className="liquid-glass-button" onClick={() => setSelectedStrategy(strategy)}>
                        View Details
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Performance</p>
                      <p className={`text-lg font-bold ${strategy.performance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {strategy.performance >= 0 ? '+' : ''}{strategy.performance}%
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Trades</p>
                      <p className="text-lg font-bold text-gray-900">{strategy.trades}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Win Rate</p>
                      <p className="text-lg font-bold text-gray-900">{strategy.winRate}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Max Drawdown</p>
                      <p className="text-lg font-bold text-red-600">{strategy.maxDrawdown}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Sharpe Ratio</p>
                      <p className="text-lg font-bold text-gray-900">{strategy.sharpe}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Last Trade</p>
                      <p className="text-sm text-gray-900">{strategy.lastTrade}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {algoTab === 'builder' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Strategy Builder</CardTitle>
              <CardDescription>Create custom trading algorithms with natural language or visual blocks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Strategy Name</label>
                  <input
                    type="text"
                    placeholder="Enter strategy name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Strategy Description</label>
                  <textarea
                    placeholder="Describe your trading strategy in natural language..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Entry Conditions</label>
                    <div className="space-y-2">
                      <div className="p-3 rounded-lg bg-white/10 border border-white/20">
                        <p className="text-sm text-gray-700">RSI &lt; 30 (Oversold)</p>
                      </div>
                      <div className="p-3 rounded-lg bg-white/10 border border-white/20">
                        <p className="text-sm text-gray-700">Volume &gt; 1.5x Average</p>
                      </div>
                      <Button variant="outline" size="sm" className="liquid-glass-button w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Condition
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Exit Conditions</label>
                    <div className="space-y-2">
                      <div className="p-3 rounded-lg bg-white/10 border border-white/20">
                        <p className="text-sm text-gray-700">RSI &gt; 70 (Overbought)</p>
                      </div>
                      <div className="p-3 rounded-lg bg-white/10 border border-white/20">
                        <p className="text-sm text-gray-700">Stop Loss: -5%</p>
                      </div>
                      <Button variant="outline" size="sm" className="liquid-glass-button w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Condition
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Button className="liquid-glass-button">
                    Save Strategy
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Test Strategy
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Import Template
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {algoTab === 'backtest' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Backtest Results</CardTitle>
                <CardDescription>Historical performance of Momentum Breakout strategy</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={Array.from({length: 30}, (_, i) => ({
                    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
                    portfolio: 10000 * (1 + (Math.random() - 0.4) * 0.3 * (i / 30)),
                    benchmark: 10000 * (1 + (Math.random() - 0.45) * 0.2 * (i / 30))
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                    <XAxis dataKey="date" stroke="#6B7280" fontSize={12} />
                    <YAxis stroke="#6B7280" fontSize={12} />
                    <Tooltip />
                    <Line type="monotone" dataKey="portfolio" stroke="#3B82F6" strokeWidth={2} name="Strategy" />
                    <Line type="monotone" dataKey="benchmark" stroke="#6B7280" strokeWidth={2} strokeDasharray="5 5" name="Benchmark" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Return</span>
                    <span className="font-semibold text-green-600">+24.7%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Benchmark Return</span>
                    <span className="font-semibold text-gray-900">+12.3%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Alpha</span>
                    <span className="font-semibold text-green-600">+12.4%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sharpe Ratio</span>
                    <span className="font-semibold text-gray-900">1.45</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Drawdown</span>
                    <span className="font-semibold text-red-600">-8.2%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Win Rate</span>
                    <span className="font-semibold text-gray-900">68%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Trade Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Trades</span>
                    <span className="font-semibold text-gray-900">156</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Winning Trades</span>
                    <span className="font-semibold text-green-600">106</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Losing Trades</span>
                    <span className="font-semibold text-red-600">50</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Win</span>
                    <span className="font-semibold text-green-600">+$245</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Loss</span>
                    <span className="font-semibold text-red-600">-$128</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Profit Factor</span>
                    <span className="font-semibold text-gray-900">1.91</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {algoTab === 'live' && (
          <div className="space-y-6">
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Live Trading Dashboard</CardTitle>
                <CardDescription>Monitor active strategies and recent trades</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { time: '14:32:15', strategy: 'Momentum Breakout', action: 'BUY', symbol: 'AAPL', shares: 50, price: 189.45, status: 'Filled' },
                    { time: '13:45:22', strategy: 'Earnings Momentum', action: 'SELL', symbol: 'GOOGL', shares: 25, price: 145.80, status: 'Filled' },
                    { time: '12:18:33', strategy: 'Momentum Breakout', action: 'BUY', symbol: 'MSFT', shares: 30, price: 385.20, status: 'Pending' }
                  ].map((trade, index) => (
                    <div key={index} className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">{trade.time}</span>
                          <span className="text-sm text-gray-600">{trade.strategy}</span>
                          <span className={`px-2 py-1 rounded text-xs font-semibold ${
                            trade.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {trade.action}
                          </span>
                          <span className="font-semibold text-gray-900">{trade.symbol}</span>
                          <span className="text-gray-700">{trade.shares} @ ${trade.price}</span>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          trade.status === 'Filled' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {trade.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Strategy Detail Modal */}
        {selectedStrategy && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setSelectedStrategy(null)}>
            <Card className="liquid-glass-card w-full max-w-4xl m-4 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{selectedStrategy.name}</CardTitle>
                    <CardDescription>{selectedStrategy.description}</CardDescription>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedStrategy(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">+{selectedStrategy.performance}%</p>
                    <p className="text-gray-600">Total Return</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedStrategy.trades}</p>
                    <p className="text-gray-600">Total Trades</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedStrategy.winRate}%</p>
                    <p className="text-gray-600">Win Rate</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedStrategy.sharpe}</p>
                    <p className="text-gray-600">Sharpe Ratio</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Button className="liquid-glass-button">
                    {selectedStrategy.status === 'Active' ? 'Pause Strategy' : 'Activate Strategy'}
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Edit Strategy
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Clone Strategy
                  </Button>
                  <Button variant="outline" className="liquid-glass-button text-red-600">
                    Delete Strategy
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    )
  }

  const renderGlobalSentiment = () => {
    const [sentimentTab, setSentimentTab] = useState('overview')
    
    const sentimentTabs = [
      { id: 'overview', label: 'Overview', icon: Globe },
      { id: 'news', label: 'News Analysis', icon: BookOpen },
      { id: 'social', label: 'Social Media', icon: Users },
      { id: 'alerts', label: 'Alerts', icon: Bell }
    ]

    const newsData = [
      {
        title: 'Federal Reserve Signals Potential Rate Cut',
        source: 'Reuters',
        sentiment: 'Positive',
        impact: 'High',
        time: '2 hours ago',
        summary: 'Fed officials hint at dovish policy stance amid economic uncertainty'
      },
      {
        title: 'Tech Earnings Beat Expectations',
        source: 'Bloomberg',
        sentiment: 'Positive',
        impact: 'Medium',
        time: '4 hours ago',
        summary: 'Major technology companies report strong quarterly results'
      },
      {
        title: 'Geopolitical Tensions Rise',
        source: 'Financial Times',
        sentiment: 'Negative',
        impact: 'High',
        time: '6 hours ago',
        summary: 'International trade disputes escalate, affecting global markets'
      }
    ]

    return (
      <div className="space-y-6">
        {/* Global Sentiment Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Global Sentiment</h1>
            <p className="text-gray-600">Real-time market sentiment from global news and social media</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </Button>
          </div>
        </div>

        {/* Sentiment Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <MetricCard
            title="Overall Sentiment"
            value="Bullish"
            change="+12% vs yesterday"
            icon={TrendingUp}
            trend="up"
          />
          <MetricCard
            title="News Sources"
            value="1,247"
            change="Active monitoring"
            icon={BookOpen}
            trend="neutral"
          />
          <MetricCard
            title="Social Mentions"
            value="45.2K"
            change="+8% vs yesterday"
            icon={Users}
            trend="up"
          />
        </div>

        {/* Sentiment Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {sentimentTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSentimentTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  sentimentTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Sentiment Content */}
        {sentimentTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sentiment Chart */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Sentiment Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={Array.from({length: 24}, (_, i) => ({
                    hour: `${i}:00`,
                    sentiment: 50 + Math.sin(i / 4) * 20 + Math.random() * 10
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="sentiment" stroke="#3B82F6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Top Trending Topics */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Trending Topics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { topic: 'Federal Reserve', mentions: 1247, sentiment: 'Positive' },
                    { topic: 'Tech Earnings', mentions: 892, sentiment: 'Positive' },
                    { topic: 'Oil Prices', mentions: 634, sentiment: 'Negative' },
                    { topic: 'Cryptocurrency', mentions: 521, sentiment: 'Neutral' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/10">
                      <div>
                        <p className="font-semibold text-gray-900">{item.topic}</p>
                        <p className="text-sm text-gray-600">{item.mentions} mentions</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-semibold ${
                        item.sentiment === 'Positive' ? 'bg-green-100 text-green-800' :
                        item.sentiment === 'Negative' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.sentiment}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {sentimentTab === 'news' && (
          <div className="space-y-6">
            {newsData.map((news, index) => (
              <Card key={index} className="liquid-glass-card">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{news.title}</h3>
                      <p className="text-gray-700 mb-3">{news.summary}</p>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-600">{news.source}</span>
                        <span className="text-sm text-gray-600">{news.time}</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        news.sentiment === 'Positive' ? 'bg-green-100 text-green-800' :
                        news.sentiment === 'Negative' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {news.sentiment}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        news.impact === 'High' ? 'bg-red-100 text-red-800' :
                        news.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {news.impact} Impact
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {sentimentTab === 'social' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Social Media Sentiment</CardTitle>
              <CardDescription>Real-time sentiment analysis from Twitter, Reddit, and other platforms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">72%</span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Twitter</h3>
                  <p className="text-sm text-gray-600">Bullish sentiment</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-orange-100 flex items-center justify-center">
                    <span className="text-2xl font-bold text-orange-600">68%</span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Reddit</h3>
                  <p className="text-sm text-gray-600">Bullish sentiment</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-2xl font-bold text-green-600">75%</span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Discord</h3>
                  <p className="text-sm text-gray-600">Bullish sentiment</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {sentimentTab === 'alerts' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Sentiment Alerts</CardTitle>
              <CardDescription>Configure alerts for significant sentiment changes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">Extreme Sentiment Change</h3>
                      <p className="text-sm text-gray-600">Alert when sentiment changes by more than 20% in 1 hour</p>
                    </div>
                    <Button variant="outline" size="sm" className="liquid-glass-button">
                      Configure
                    </Button>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">Breaking News Impact</h3>
                      <p className="text-sm text-gray-600">Alert for high-impact news with significant sentiment shift</p>
                    </div>
                    <Button variant="outline" size="sm" className="liquid-glass-button">
                      Configure
                    </Button>
                  </div>
                </div>
                
                <Button className="liquid-glass-button w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Alert
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  const renderModelTrainer = () => {
    const [trainerTab, setTrainerTab] = useState('models')
    const [selectedModel, setSelectedModel] = useState(null)
    
    const trainerTabs = [
      { id: 'models', label: 'My Models', icon: Brain },
      { id: 'training', label: 'Training', icon: Activity },
      { id: 'marketplace', label: 'Marketplace', icon: Globe },
      { id: 'performance', label: 'Performance', icon: BarChart3 }
    ]

    const models = [
      {
        id: 1,
        name: 'Tech Stock Predictor',
        type: 'Price Prediction',
        accuracy: 87.3,
        status: 'Active',
        lastTrained: '2 days ago',
        predictions: 1247,
        description: 'Specialized model for predicting technology stock movements'
      },
      {
        id: 2,
        name: 'Earnings Surprise Model',
        type: 'Earnings Analysis',
        accuracy: 72.8,
        status: 'Training',
        lastTrained: '1 week ago',
        predictions: 892,
        description: 'Predicts earnings surprises based on pre-announcement indicators'
      },
      {
        id: 3,
        name: 'Volatility Forecaster',
        type: 'Risk Analysis',
        accuracy: 91.2,
        status: 'Active',
        lastTrained: '3 days ago',
        predictions: 634,
        description: 'Forecasts market volatility using multiple data sources'
      }
    ]

    return (
      <div className="space-y-6">
        {/* Model Trainer Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Model Trainer</h1>
            <p className="text-gray-600">Train and deploy personalized AI models for trading</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="liquid-glass-button">
              <Plus className="h-4 w-4 mr-2" />
              New Model
            </Button>
            <Button variant="outline" className="liquid-glass-button">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Training Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <MetricCard
            title="Active Models"
            value="3"
            change="2 training"
            icon={Brain}
            trend="up"
          />
          <MetricCard
            title="Avg Accuracy"
            value="83.8%"
            change="+2.1% this week"
            icon={TrendingUp}
            trend="up"
          />
          <MetricCard
            title="Predictions Made"
            value="2,773"
            change="Today: 127"
            icon={Activity}
            trend="neutral"
          />
          <MetricCard
            title="Training Hours"
            value="156"
            change="This month"
            icon={BarChart3}
            trend="neutral"
          />
        </div>

        {/* Model Trainer Tabs */}
        <div className="flex space-x-1 bg-white/20 backdrop-blur-sm rounded-lg p-1">
          {trainerTabs.map((tab) => {
            const IconComponent = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setTrainerTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  trainerTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Model Trainer Content */}
        {trainerTab === 'models' && (
          <div className="space-y-6">
            {models.map((model) => (
              <Card key={model.id} className="liquid-glass-card">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-4 h-4 rounded-full ${
                        model.status === 'Active' ? 'bg-green-500' : 
                        model.status === 'Training' ? 'bg-yellow-500 animate-pulse' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{model.name}</h3>
                        <p className="text-gray-600">{model.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        model.status === 'Active' ? 'bg-green-100 text-green-800' :
                        model.status === 'Training' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {model.status}
                      </span>
                      <Button variant="outline" size="sm" className="liquid-glass-button" onClick={() => setSelectedModel(model)}>
                        View Details
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Type</p>
                      <p className="text-lg font-bold text-gray-900">{model.type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Accuracy</p>
                      <p className="text-lg font-bold text-green-600">{model.accuracy}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Predictions</p>
                      <p className="text-lg font-bold text-gray-900">{model.predictions}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Last Trained</p>
                      <p className="text-sm text-gray-900">{model.lastTrained}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Status</p>
                      <p className="text-sm text-gray-900">{model.status}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {trainerTab === 'training' && (
          <Card className="liquid-glass-card">
            <CardHeader>
              <CardTitle>Model Training Center</CardTitle>
              <CardDescription>Create and train new AI models for your trading strategy</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Model Name</label>
                  <input
                    type="text"
                    placeholder="Enter model name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Model Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm">
                    <option>Price Prediction</option>
                    <option>Earnings Analysis</option>
                    <option>Risk Analysis</option>
                    <option>Sentiment Analysis</option>
                    <option>Technical Analysis</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Training Data</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <h3 className="font-semibold text-gray-900 mb-2">Historical Prices</h3>
                      <p className="text-sm text-gray-600">5 years of OHLCV data</p>
                      <div className="mt-2">
                        <input type="checkbox" className="mr-2" defaultChecked />
                        <span className="text-sm">Include in training</span>
                      </div>
                    </div>
                    <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <h3 className="font-semibold text-gray-900 mb-2">Technical Indicators</h3>
                      <p className="text-sm text-gray-600">RSI, MACD, Bollinger Bands</p>
                      <div className="mt-2">
                        <input type="checkbox" className="mr-2" defaultChecked />
                        <span className="text-sm">Include in training</span>
                      </div>
                    </div>
                    <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <h3 className="font-semibold text-gray-900 mb-2">News Sentiment</h3>
                      <p className="text-sm text-gray-600">Financial news analysis</p>
                      <div className="mt-2">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Include in training</span>
                      </div>
                    </div>
                    <div className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <h3 className="font-semibold text-gray-900 mb-2">Economic Data</h3>
                      <p className="text-sm text-gray-600">GDP, inflation, employment</p>
                      <div className="mt-2">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Include in training</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Button className="liquid-glass-button">
                    Start Training
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Save Draft
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Load Template
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {trainerTab === 'marketplace' && (
          <div className="space-y-6">
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Model Marketplace</CardTitle>
                <CardDescription>Discover and download pre-trained models from the community</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    { name: 'S&P 500 Predictor', author: 'QuantTrader', rating: 4.8, downloads: 1247, price: 'Free' },
                    { name: 'Crypto Volatility Model', author: 'CryptoAI', rating: 4.6, downloads: 892, price: '$29' },
                    { name: 'Earnings Beat Predictor', author: 'WallStreetML', rating: 4.9, downloads: 634, price: '$49' }
                  ].map((model, index) => (
                    <div key={index} className="p-4 rounded-lg bg-white/10 border border-white/20">
                      <h3 className="font-semibold text-gray-900 mb-2">{model.name}</h3>
                      <p className="text-sm text-gray-600 mb-3">by {model.author}</p>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm font-semibold">{model.rating}</span>
                        </div>
                        <span className="text-sm text-gray-600">{model.downloads} downloads</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-gray-900">{model.price}</span>
                        <Button size="sm" className="liquid-glass-button">
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {trainerTab === 'performance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Model Performance Chart */}
            <Card className="liquid-glass-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Model Performance Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={Array.from({length: 30}, (_, i) => ({
                    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
                    accuracy: 80 + Math.sin(i / 5) * 10 + Math.random() * 5
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[70, 100]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="accuracy" stroke="#3B82F6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            {/* Model Comparison */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Model Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {models.map((model, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/10">
                      <div>
                        <p className="font-semibold text-gray-900">{model.name}</p>
                        <p className="text-sm text-gray-600">{model.type}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">{model.accuracy}%</p>
                        <p className="text-sm text-gray-600">{model.predictions} predictions</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            {/* Training Progress */}
            <Card className="liquid-glass-card">
              <CardHeader>
                <CardTitle>Current Training</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Earnings Surprise Model</span>
                      <span className="text-sm text-gray-600">67%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '67%' }}></div>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">Estimated completion: 2 hours</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Model Detail Modal */}
        {selectedModel && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setSelectedModel(null)}>
            <Card className="liquid-glass-card w-full max-w-4xl m-4 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{selectedModel.name}</CardTitle>
                    <CardDescription>{selectedModel.description}</CardDescription>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedModel(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{selectedModel.accuracy}%</p>
                    <p className="text-gray-600">Accuracy</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedModel.predictions}</p>
                    <p className="text-gray-600">Predictions</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedModel.type}</p>
                    <p className="text-gray-600">Model Type</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{selectedModel.lastTrained}</p>
                    <p className="text-gray-600">Last Trained</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Button className="liquid-glass-button">
                    Retrain Model
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Export Model
                  </Button>
                  <Button variant="outline" className="liquid-glass-button">
                    Clone Model
                  </Button>
                  <Button variant="outline" className="liquid-glass-button text-red-600">
                    Delete Model
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    )
  }

  const renderContent = () => {
    if (activeSection === 'dashboard') {
      return !isLoggedIn ? (
        <>
          <div className="text-center py-20">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-5xl font-bold text-gray-900 mb-6">
                Slynqix
              </h1>
              <p className="text-xl text-gray-600 mb-12">
                Comprehensive Stock Market Analysis & Paper Trading Platform
              </p>
              
              {/* Core Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                {coreFeatures.map((feature) => {
                  const IconComponent = feature.icon
                  return (
                    <div key={feature.id} className="liquid-glass-card p-8 text-left">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                      </div>
                      <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                    </div>
                  )
                })}
              </div>

              {/* Authentication Section */}
              <div className="max-w-md mx-auto">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Get Started</h2>
                <p className="text-gray-600 mb-8">Choose your preferred authentication method</p>
                <Card className="liquid-glass-card">
                  <CardContent className="p-8 space-y-4">
                    <AuthButton 
                      provider="Google" 
                      icon={Chrome} 
                      onClick={() => setIsLoggedIn(true)}
                    />
                    <AuthButton 
                      provider="GitHub" 
                      icon={Github} 
                      onClick={() => setIsLoggedIn(true)}
                    />
                    <AuthButton 
                      provider="Phone" 
                      icon={Phone} 
                      onClick={() => setIsLoggedIn(true)}
                    />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </>
      ) : (
        renderDashboard()
      )
    } else if (activeSection === 'console') {
      return renderConsole()
    } else if (activeSection === 'journal') {
      return renderJournal()
    } else if (activeSection === 'mindsage') {
      return renderMindsage()
    } else if (activeSection === 'algo-trading') {
      return renderAlgoTrading()
    } else if (activeSection === 'global-sentiment') {
      return renderGlobalSentiment()
    } else if (activeSection === 'model-trainer') {
      return renderModelTrainer()
    } else {
      // Placeholder content for other sections
      const currentFeature = coreFeatures.find(f => f.id === activeSection)
      const IconComponent = currentFeature?.icon || Settings
      
      return (
        <div className="text-center py-20">
          <Card className="liquid-glass-card max-w-md mx-auto">
            <CardContent className="p-12">
              <IconComponent className="h-16 w-16 text-gray-400 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                {currentFeature?.title || 'Coming Soon'}
              </h2>
              <p className="text-gray-600 mb-6">
                {currentFeature?.description || 'This section is under development.'}
              </p>
              <Button 
                onClick={() => setActiveSection('dashboard')}
                className="liquid-glass-button text-gray-700"
              >
                Back to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    }
  }

  return (
    <div className="min-h-screen liquid-glass-bg">
      {isLoggedIn && (
        <>
          {/* Sidebar */}
          <div className={`fixed left-0 top-0 h-full bg-white/10 backdrop-blur-md border-r border-white/20 transition-all duration-300 z-40 ${
            sidebarOpen ? 'w-64' : 'w-16'
          }`}>
            <div className="p-4">
              <div className="flex items-center gap-3 mb-8">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="p-2 hover:bg-white/20"
                >
                  <Menu className="h-5 w-5 text-gray-700" />
                </Button>
                {sidebarOpen && (
                  <h1 className="text-xl font-bold text-gray-900">Slynqix</h1>
                )}
              </div>
              
              <nav className="space-y-2">
                {sidebarItems.map((item) => {
                  const IconComponent = item.icon
                  const isActive = activeSection === item.id
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveSection(item.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                        isActive 
                          ? 'bg-white/20 text-gray-900 border border-white/30' 
                          : 'text-gray-700 hover:bg-white/10 hover:text-gray-900'
                      }`}
                    >
                      <IconComponent className="h-5 w-5 flex-shrink-0" />
                      {sidebarOpen && (
                        <span className="font-medium">{item.label}</span>
                      )}
                    </button>
                  )
                })}
              </nav>
              
              {sidebarOpen && (
                <div className="absolute bottom-4 left-4 right-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsLoggedIn(false)}
                    className="w-full liquid-glass-button text-gray-700"
                  >
                    <LogIn className="h-4 w-4 mr-2" />
                    Logout
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
            <div className="p-6">
              {renderContent()}
            </div>
          </div>
        </>
      )}

      {!isLoggedIn && (
        <div className="p-6">
          {renderContent()}
        </div>
      )}
    </div>
  )
}

export default App

