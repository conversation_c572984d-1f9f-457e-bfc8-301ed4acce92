import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { 
  BarChart3, 
  BookOpen, 
  Brain, 
  TrendingUp, 
  Globe, 
  Cpu, 
  LogIn,
  Github,
  Phone,
  Chrome,
  Menu,
  X,
  Home,
  User,
  Settings
} from 'lucide-react'
import './App.css'

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [activeSection, setActiveSection] = useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(true)

  const coreFeatures = [
    {
      id: 'console',
      title: 'Console',
      description: 'In-depth analysis tools for market symbols with statistical, visual, and indicator analysis',
      icon: BarChart3,
      color: 'from-blue-500/20 to-cyan-500/20'
    },
    {
      id: 'journal',
      title: 'Journal',
      description: 'Log and review your trading activities with detailed performance tracking',
      icon: BookO<PERSON>,
      color: 'from-green-500/20 to-emerald-500/20'
    },
    {
      id: 'mindsage',
      title: 'Mindsage',
      description: 'AI-powered investment guide providing insights beyond OHLCV data',
      icon: Brain,
      color: 'from-purple-500/20 to-violet-500/20'
    },
    {
      id: 'algo-trading',
      title: 'Algo Trading',
      description: 'Automate paper trades with custom algorithms and natural language commands',
      icon: TrendingUp,
      color: 'from-orange-500/20 to-red-500/20'
    },
    {
      id: 'global-sentiment',
      title: 'Global Sentiment',
      description: 'Analyze news sentiment from global financial platforms for market insights',
      icon: Globe,
      color: 'from-teal-500/20 to-blue-500/20'
    },
    {
      id: 'model-trainer',
      title: 'Model Trainer',
      description: 'Train personalized AI models to act as your personal trading assistant',
      icon: Cpu,
      color: 'from-pink-500/20 to-rose-500/20'
    }
  ]

  const sidebarItems = [
    { id: 'dashboard', title: 'Dashboard', icon: Home },
    { id: 'console', title: 'Console', icon: BarChart3 },
    { id: 'journal', title: 'Journal', icon: BookOpen },
    { id: 'mindsage', title: 'Mindsage', icon: Brain },
    { id: 'algo-trading', title: 'Algo Trading', icon: TrendingUp },
    { id: 'global-sentiment', title: 'Global Sentiment', icon: Globe },
    { id: 'model-trainer', title: 'Model Trainer', icon: Cpu },
    { id: 'profile', title: 'Profile', icon: User },
    { id: 'settings', title: 'Settings', icon: Settings }
  ]

  const LoginButton = ({ icon: Icon, provider, onClick }) => (
    <Button
      onClick={onClick}
      className="liquid-glass-button group relative overflow-hidden border border-gray-200 bg-white/80 backdrop-blur-md hover:bg-white/90 transition-all duration-300 hover:scale-105 text-gray-700 hover:text-gray-900"
      variant="outline"
    >
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4" />
        <span>Continue with {provider}</span>
      </div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
    </Button>
  )

  const FeatureCard = ({ feature, onClick }) => {
    const IconComponent = feature.icon
    
    return (
      <Card 
        className="liquid-glass-card group relative overflow-hidden border border-gray-200 bg-white/80 backdrop-blur-md hover:bg-white/90 transition-all duration-500 hover:scale-105 hover:shadow-xl cursor-pointer"
        onClick={() => onClick(feature.id)}
      >
        <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
        
        <CardHeader className="relative z-10">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100/80 backdrop-blur-sm group-hover:bg-white/90 transition-colors duration-300">
              <IconComponent className="h-6 w-6 text-gray-700" />
            </div>
            <CardTitle className="text-gray-800 text-lg font-semibold">
              {feature.title}
            </CardTitle>
          </div>
        </CardHeader>
        
        <CardContent className="relative z-10">
          <CardDescription className="text-gray-600 leading-relaxed">
            {feature.description}
          </CardDescription>
        </CardContent>
      </Card>
    )
  }

  const SidebarItem = ({ item, isActive, onClick }) => {
    const IconComponent = item.icon
    
    return (
      <button
        onClick={() => onClick(item.id)}
        className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 ${
          isActive 
            ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500' 
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
        }`}
      >
        <IconComponent className="h-5 w-5" />
        {sidebarOpen && <span className="font-medium">{item.title}</span>}
      </button>
    )
  }

  const renderContent = () => {
    if (activeSection === 'dashboard') {
      return (
        <>
          {!isLoggedIn ? (
            <>
              {/* Core Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                {coreFeatures.map((feature) => (
                  <FeatureCard 
                    key={feature.id} 
                    feature={feature} 
                    onClick={(id) => setActiveSection(id)}
                  />
                ))}
              </div>

              {/* Login Section */}
              <div className="max-w-md mx-auto">
                <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md">
                  <CardHeader className="text-center">
                    <CardTitle className="text-gray-800 text-2xl mb-2">Get Started</CardTitle>
                    <CardDescription className="text-gray-600">
                      Choose your preferred authentication method
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <LoginButton 
                      icon={Chrome} 
                      provider="Google" 
                      onClick={() => setIsLoggedIn(true)}
                    />
                    <LoginButton 
                      icon={Github} 
                      provider="GitHub" 
                      onClick={() => setIsLoggedIn(true)}
                    />
                    <LoginButton 
                      icon={Phone} 
                      provider="Phone" 
                      onClick={() => setIsLoggedIn(true)}
                    />
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <>
              {/* After Login Dashboard */}
              <div className="mb-12">
                <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-gray-800 text-xl">Live Market Overview</CardTitle>
                    <CardDescription className="text-gray-600">
                      Real-time TradingView chart - Nifty 50 (Default)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center border border-gray-200">
                      <div className="text-center">
                        <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">TradingView Chart Integration</p>
                        <p className="text-gray-500 text-sm mt-2">Nifty 50 - Live Data</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Performance Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-600 text-sm">Total Paper PnL</p>
                        <p className="text-2xl font-bold text-green-600">+5.2%</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-600 text-sm">Active Algos</p>
                        <p className="text-2xl font-bold text-blue-600">2</p>
                      </div>
                      <Cpu className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-600 text-sm">Win Rate</p>
                        <p className="text-2xl font-bold text-purple-600">68%</p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Core Features Grid for Logged In Users */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {coreFeatures.map((feature) => (
                  <FeatureCard 
                    key={feature.id} 
                    feature={feature} 
                    onClick={(id) => setActiveSection(id)}
                  />
                ))}
              </div>
            </>
          )}
        </>
      )
    } else {
      // Placeholder content for other sections
      const currentFeature = coreFeatures.find(f => f.id === activeSection)
      const IconComponent = currentFeature?.icon || Settings
      
      return (
        <div className="text-center py-20">
          <Card className="liquid-glass-card border border-gray-200 bg-white/80 backdrop-blur-md max-w-md mx-auto">
            <CardContent className="p-12">
              <IconComponent className="h-16 w-16 text-gray-400 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                {currentFeature?.title || 'Coming Soon'}
              </h2>
              <p className="text-gray-600 mb-6">
                {currentFeature?.description || 'This section is under development.'}
              </p>
              <Button 
                onClick={() => setActiveSection('dashboard')}
                className="liquid-glass-button border border-gray-200 bg-white/80 hover:bg-white/90 text-gray-700"
              >
                Back to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    }
  }

  return (
    <div className="min-h-screen bg-white flex">
      {/* Sidebar */}
      {isLoggedIn && (
        <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 bg-white border-r border-gray-200 flex flex-col`}>
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {sidebarOpen && (
                <h1 className="text-xl font-bold text-gray-800">Slynqix</h1>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="text-gray-600 hover:text-gray-800"
              >
                {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Sidebar Navigation */}
          <div className="flex-1 p-4 space-y-2">
            {sidebarItems.map((item) => (
              <SidebarItem
                key={item.id}
                item={item}
                isActive={activeSection === item.id}
                onClick={setActiveSection}
              />
            ))}
          </div>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-gray-200">
            <Button
              onClick={() => {
                setIsLoggedIn(false)
                setActiveSection('dashboard')
              }}
              className="w-full liquid-glass-button border border-gray-200 bg-white/80 hover:bg-white/90 text-gray-700"
              variant="outline"
            >
              <LogIn className="h-4 w-4 mr-2" />
              {sidebarOpen && 'Logout'}
            </Button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {/* Top Header for non-logged in users */}
        {!isLoggedIn && (
          <div className="text-center py-12 px-6">
            <h1 className="text-6xl font-bold text-gray-800 mb-4 tracking-tight">
              <span className="bg-gradient-to-r from-gray-800 via-blue-600 to-gray-800 bg-clip-text text-transparent">
                Slynqix
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Comprehensive Stock Market Analysis & Paper Trading Platform
            </p>
            <div className="mt-8 w-24 h-1 bg-gradient-to-r from-transparent via-gray-400 to-transparent mx-auto" />
          </div>
        )}

        {/* Content Area */}
        <div className="container mx-auto px-6 py-8">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default App

