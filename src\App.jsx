import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { 
  BarChart3, 
  BookOpen, 
  Brain, 
  TrendingUp, 
  Globe, 
  Cpu, 
  LogIn,
  Github,
  Phone,
  Chrome
} from 'lucide-react'
import './App.css'

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  const coreFeatures = [
    {
      id: 'console',
      title: 'Console',
      description: 'In-depth analysis tools for market symbols with statistical, visual, and indicator analysis',
      icon: BarChart3,
      color: 'from-blue-500/20 to-cyan-500/20'
    },
    {
      id: 'journal',
      title: 'Journal',
      description: 'Log and review your trading activities with detailed performance tracking',
      icon: BookOpen,
      color: 'from-green-500/20 to-emerald-500/20'
    },
    {
      id: 'mindsage',
      title: 'Mindsage',
      description: 'AI-powered investment guide providing insights beyond OHLCV data',
      icon: Brain,
      color: 'from-purple-500/20 to-violet-500/20'
    },
    {
      id: 'algo-trading',
      title: 'Algo Trading',
      description: 'Automate paper trades with custom algorithms and natural language commands',
      icon: TrendingUp,
      color: 'from-orange-500/20 to-red-500/20'
    },
    {
      id: 'global-sentiment',
      title: 'Global Sentiment',
      description: 'Analyze news sentiment from global financial platforms for market insights',
      icon: Globe,
      color: 'from-teal-500/20 to-blue-500/20'
    },
    {
      id: 'model-trainer',
      title: 'Model Trainer',
      description: 'Train personalized AI models to act as your personal trading assistant',
      icon: Cpu,
      color: 'from-pink-500/20 to-rose-500/20'
    }
  ]

  const LoginButton = ({ icon: Icon, provider, onClick }) => (
    <Button
      onClick={onClick}
      className="liquid-glass-button group relative overflow-hidden border-0 bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all duration-300 hover:scale-105"
      variant="outline"
    >
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4" />
        <span>Continue with {provider}</span>
      </div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
    </Button>
  )

  const FeatureCard = ({ feature }) => {
    const IconComponent = feature.icon
    
    return (
      <Card className="liquid-glass-card group relative overflow-hidden border-0 bg-white/5 backdrop-blur-md hover:bg-white/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl">
        <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
        
        <CardHeader className="relative z-10">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-white/10 backdrop-blur-sm group-hover:bg-white/20 transition-colors duration-300">
              <IconComponent className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-white text-lg font-semibold">
              {feature.title}
            </CardTitle>
          </div>
        </CardHeader>
        
        <CardContent className="relative z-10">
          <CardDescription className="text-white/80 leading-relaxed">
            {feature.description}
          </CardDescription>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="min-h-screen liquid-glass-background relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-teal-500/10 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-white mb-4 tracking-tight">
            <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
              Slynqix
            </span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Comprehensive Stock Market Analysis & Paper Trading Platform
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent mx-auto" />
        </div>

        {!isLoggedIn ? (
          <>
            {/* Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {coreFeatures.map((feature) => (
                <FeatureCard key={feature.id} feature={feature} />
              ))}
            </div>

            {/* Login Section */}
            <div className="max-w-md mx-auto">
              <Card className="liquid-glass-card border-0 bg-white/5 backdrop-blur-md">
                <CardHeader className="text-center">
                  <CardTitle className="text-white text-2xl mb-2">Get Started</CardTitle>
                  <CardDescription className="text-white/70">
                    Choose your preferred authentication method
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <LoginButton 
                    icon={Chrome} 
                    provider="Google" 
                    onClick={() => setIsLoggedIn(true)}
                  />
                  <LoginButton 
                    icon={Github} 
                    provider="GitHub" 
                    onClick={() => setIsLoggedIn(true)}
                  />
                  <LoginButton 
                    icon={Phone} 
                    provider="Phone" 
                    onClick={() => setIsLoggedIn(true)}
                  />
                </CardContent>
              </Card>
            </div>
          </>
        ) : (
          <>
            {/* After Login Dashboard */}
            <div className="mb-12">
              <Card className="liquid-glass-card border-0 bg-white/5 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-white text-xl">Live Market Overview</CardTitle>
                  <CardDescription className="text-white/70">
                    Real-time TradingView chart - Nifty 50 (Default)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-lg flex items-center justify-center border border-white/10">
                    <div className="text-center">
                      <BarChart3 className="h-16 w-16 text-white/50 mx-auto mb-4" />
                      <p className="text-white/70">TradingView Chart Integration</p>
                      <p className="text-white/50 text-sm mt-2">Nifty 50 - Live Data</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <Card className="liquid-glass-card border-0 bg-white/5 backdrop-blur-md">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white/70 text-sm">Total Paper PnL</p>
                      <p className="text-2xl font-bold text-green-400">+5.2%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="liquid-glass-card border-0 bg-white/5 backdrop-blur-md">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white/70 text-sm">Active Algos</p>
                      <p className="text-2xl font-bold text-blue-400">2</p>
                    </div>
                    <Cpu className="h-8 w-8 text-blue-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="liquid-glass-card border-0 bg-white/5 backdrop-blur-md">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white/70 text-sm">Win Rate</p>
                      <p className="text-2xl font-bold text-purple-400">68%</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-400" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Core Features Grid for Logged In Users */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {coreFeatures.map((feature) => (
                <FeatureCard key={feature.id} feature={feature} />
              ))}
            </div>

            {/* Logout Button */}
            <div className="text-center mt-12">
              <Button
                onClick={() => setIsLoggedIn(false)}
                className="liquid-glass-button bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all duration-300"
                variant="outline"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default App

